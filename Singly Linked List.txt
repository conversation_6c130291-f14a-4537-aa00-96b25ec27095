#include <iostream>
#include <stack>
#include <queue>

using namespace std;


class Node{
   public:
        int value ;
        Node * next ;
};

void InsertAtBiginning (Node * &head , int value) {
   // Allocate Memory to a node 
   Node * new_node = new Node();

   // insert the data 
   new_node-> value =value ;
   new_node-> next = head ; 

   // move head to new node
   head = new_node;
}

// print the linked list 
void printLinkedList(Node * head){
   while (head != NULL){
      cout << head->value << endl;
      head = head->next;
   }
}

   // Find 

Node  *Find(Node *head , int value ){
   while (head != NULL ){
      if (head->value == value){
         return head;
      }
      head = head->next;
   }
   return NULL;
}


// Insert After
void InsertAfter (Node * prev_node , int value ){
   if (prev_node ==  NULL ){
      cout << "the given previous node cannot be null" << endl;
      return ;
   }

   Node * new_node = new Node();
   new_node->value = value ;
   new_node->next = prev_node-> next;
   prev_node->next = new_node;
}

// Insert At End

void InsertAtEnd(Node * &head , int value ){
   Node * new_node = new Node();

   new_node->value = value;
   new_node-> next = NULL;

   if (head ==NULL){
      head = new_node;
      return ;
   }

   Node * LastNode = head;
   while (LastNode->next != NULL){
      LastNode = LastNode->next;
   }
   LastNode->next = new_node;
   return;
}

// Delete Node

void DeleteNode(Node * &head , int value ){
   Node * current = head , * prev = head ;

   if (head == NULL){
      return;
   }

   if (current -> value == value){
      head = current -> next ;
      delete current;
      return;
   }

   // find the key to be deleted 
   while (current != NULL && current -> value != value){
      prev = current ;
      current = current -> next ;
   }

   // if the value is not present 
   if (current == NULL){
      return;
   }

   // Remove the node 
   prev->next = current->next;
   delete current; // Free from memory 

}

 // Delete First Node
void DeleteFirstNode (Node * &head){
   Node * current = head ;

   if (head == NULL){
      return ;
   } 

   head = current -> next ;
   delete current; // free from memory  
   return;
}

void DeleteLastNode(Node * &head){

   Node * current = head , * prev = head ;

   if (head == NULL){
      return ;
   }
   
   if (current -> next == NULL){
      head = NULL ;
      delete current; // free from memory
      return;
   } 

   // find the key to be deleted 
   while (current != NULL && current -> next != NULL){
      prev = current ;
      current = current -> next ;
   }

   // Remove the node
   prev-> next = NULL ;
   delete current;
}



int main(){
 
   Node * head = NULL ;

   InsertAtBiginning(head , 1);
   InsertAtBiginning(head , 2);
   InsertAtBiginning(head , 3);
   InsertAtBiginning(head , 4);
   InsertAtBiginning(head , 5);
   

   
    Node *N1 = Find(head , 3);

    if (N1 != NULL){
      cout << "Node Found 👌" << endl;
    }else {
      cout << "Node Is Not Found 😒 " << endl;
    }

   cout << "--------------InsertAfter------------------" << endl;
 

   InsertAfter(N1 , 500);

   printLinkedList(head);


      cout << "--------------InsertAtEnd------------------" << endl;


   InsertAtEnd(head , 0);


   printLinkedList(head);

   cout << "----------------DeleteNode----------------" << endl;

   DeleteNode(head , 500);

   printLinkedList(head);

   cout << "---------------DeleteFirstNode-----------------" << endl;

   DeleteFirstNode(head);

   printLinkedList(head);

   cout << "---------------DeleteLastNode-----------------" << endl;

   DeleteLastNode(head);

   printLinkedList(head);




   return 0;
}
