#include <iostream>
#include <stack>
#include <queue>

using namespace std;


class Node{
   public:
        int value ;
        Node * next ;
        Node * prev ;
};

void InsertAtBeginning (Node * &head , int value) {

   /*
   1- Create a new node with the desired value.
   2- Set the next pointer of the new node to point to the current head of the list.
   3- Set the previous pointer of the current head to the new node.
   4- Set the new node as the new head of the list.
   */

   Node * newnode = new Node();
   newnode -> value = value;
   newnode -> next = head;
   newnode -> prev = NULL;

   if (head != NULL){
      head -> prev = newnode;
   }
   head = newnode;
}

void printnodedetails (Node * head){
   if (head -> prev != NULL){
      cout << head-> prev->value ;
   }else{
      cout << "NULL";
   }

   cout << " <--> " << head -> value << " <--> ";

   if (head -> next != NULL){
      cout << head->next->value << endl;
   }else{
      cout << " NULL" << endl;
   }
}

// Print the linked list
void PrintListDetails(Node* head)

{
    cout << "\n\n";
    while (head != NULL) {
        printnodedetails(head);
        head = head->next;
    }
}

// Print the linked list
void PrintList(Node* head)

{
    cout << "NULL <--> ";
    while (head != NULL) {
        cout << head->value << "  <-->  ";
        head = head->next;
    }
    cout << "NULL";

}



int main(){

   Node * head = NULL ;
 
   InsertAtBeginning(head, 5);
    InsertAtBeginning(head, 4);
    InsertAtBeginning(head, 3);
    InsertAtBeginning(head, 2);
    InsertAtBeginning(head, 1);

    cout << "\nLinked List Contenet:\n";
    PrintList(head);
    PrintListDetails(head);

    

   


   return 0;
}
